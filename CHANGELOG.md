# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Initial project setup with Next.js 15
- Streaming AI chat interface using assistant-ui
- Integration with Anthropic Claude 3.5 Sonnet
- n8n Model Context Protocol (MCP) server integration
- Real-time streaming responses with Server-Sent Events
- Modern UI with Tailwind CSS and Radix UI components
- Responsive design for desktop and mobile
- TypeScript support throughout the application
- ESLint and Prettier configuration
- Comprehensive documentation and contributing guidelines

### Features
- **Chat Interface**: Real-time conversation with AI
- **Tool Integration**: AI can execute n8n workflows
- **Streaming Responses**: Live response generation
- **Modern UI/UX**: Clean, accessible interface
- **MCP Integration**: Direct access to n8n automation tools

### Technical
- Next.js App Router architecture
- Vercel AI SDK for streaming
- Model Context Protocol client
- Server-side API routes
- Client-side React components
- TypeScript type safety
- Tailwind CSS styling
- Radix UI primitives

## [1.0.0] - 2024-12-20

### Added
- Initial release of Streaming N8N Chatbot
- Core chat functionality with Claude 3.5 Sonnet
- n8n MCP server integration
- Streaming response capabilities
- Modern React-based UI
- Comprehensive documentation
- MIT License
- Contributing guidelines
- GitHub repository setup

### Infrastructure
- Git repository initialization
- GitHub-ready project structure
- CI/CD preparation
- Development environment setup
- Production build configuration

---

## Release Notes Format

### Types of Changes
- **Added** for new features
- **Changed** for changes in existing functionality
- **Deprecated** for soon-to-be removed features
- **Removed** for now removed features
- **Fixed** for any bug fixes
- **Security** for vulnerability fixes

### Version Numbering
This project follows [Semantic Versioning](https://semver.org/):
- **MAJOR** version for incompatible API changes
- **MINOR** version for backwards-compatible functionality additions
- **PATCH** version for backwards-compatible bug fixes

### Links
- [Unreleased]: Compare with latest release
- [1.0.0]: Initial release tag
