name: 🚀 Feature Request
description: Suggest a new feature or enhancement
title: "[Feature]: "
labels: ["enhancement", "needs-triage"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        Thank you for suggesting a new feature! Please fill out the form below to help us understand your request.

  - type: textarea
    id: summary
    attributes:
      label: Feature Summary
      description: A brief summary of the feature you'd like to see.
      placeholder: Briefly describe the feature...
    validations:
      required: true

  - type: textarea
    id: problem
    attributes:
      label: Problem Statement
      description: What problem does this feature solve? What use case does it address?
      placeholder: |
        Is your feature request related to a problem? Please describe.
        A clear and concise description of what the problem is.
        Ex. I'm always frustrated when [...]
    validations:
      required: true

  - type: textarea
    id: solution
    attributes:
      label: Proposed Solution
      description: Describe the solution you'd like to see implemented.
      placeholder: |
        A clear and concise description of what you want to happen.
        How should this feature work?
    validations:
      required: true

  - type: textarea
    id: alternatives
    attributes:
      label: Alternative Solutions
      description: Have you considered any alternative solutions or workarounds?
      placeholder: |
        A clear and concise description of any alternative solutions or features you've considered.
        Are there any existing workarounds?

  - type: dropdown
    id: priority
    attributes:
      label: Priority
      description: How important is this feature to you?
      options:
        - Low - Nice to have
        - Medium - Would be helpful
        - High - Important for my use case
        - Critical - Blocking my work
    validations:
      required: true

  - type: dropdown
    id: complexity
    attributes:
      label: Estimated Complexity
      description: How complex do you think this feature would be to implement?
      options:
        - Simple - Small change or addition
        - Medium - Moderate development effort
        - Complex - Significant development effort
        - Unknown - Not sure about complexity

  - type: textarea
    id: examples
    attributes:
      label: Examples and Use Cases
      description: Provide specific examples of how this feature would be used.
      placeholder: |
        Example scenarios:
        1. When a user wants to...
        2. In the case where...
        3. For workflows that involve...

  - type: textarea
    id: mockups
    attributes:
      label: Mockups or References
      description: If applicable, provide mockups, wireframes, or references to similar features.
      placeholder: |
        Links to similar features in other tools:
        - Tool A: [link]
        - Tool B: [link]
        
        Or drag and drop mockup images here...

  - type: textarea
    id: technical
    attributes:
      label: Technical Considerations
      description: Any technical details, constraints, or considerations for this feature?
      placeholder: |
        - API changes needed
        - Database schema changes
        - Performance considerations
        - Security implications
        - Compatibility requirements

  - type: checkboxes
    id: contribution
    attributes:
      label: Contribution
      description: Are you willing to contribute to this feature?
      options:
        - label: I am willing to help implement this feature
          required: false
        - label: I can provide testing and feedback
          required: false
        - label: I can help with documentation
          required: false

  - type: checkboxes
    id: checklist
    attributes:
      label: Checklist
      description: Please confirm the following before submitting.
      options:
        - label: I have searched for existing feature requests that might be similar
          required: true
        - label: I have provided a clear description of the problem and solution
          required: true
        - label: I have considered the impact and complexity of this feature
          required: true
