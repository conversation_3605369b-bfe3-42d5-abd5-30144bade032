name: ❓ Question
description: Ask a question about the project
title: "[Question]: "
labels: ["question", "needs-triage"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        Thank you for your question! Please fill out the form below to help us provide the best answer.

  - type: textarea
    id: question
    attributes:
      label: Your Question
      description: What would you like to know?
      placeholder: Please describe your question in detail...
    validations:
      required: true

  - type: dropdown
    id: category
    attributes:
      label: Question Category
      description: What category does your question fall into?
      options:
        - Installation and Setup
        - Configuration
        - Usage and Features
        - n8n Integration
        - API and Development
        - Troubleshooting
        - Performance
        - Security
        - Deployment
        - Other
    validations:
      required: true

  - type: textarea
    id: context
    attributes:
      label: Context
      description: Please provide any relevant context for your question.
      placeholder: |
        - What are you trying to achieve?
        - What have you already tried?
        - Any relevant configuration or setup details?

  - type: textarea
    id: environment
    attributes:
      label: Environment (if relevant)
      description: Please provide environment details if they're relevant to your question.
      placeholder: |
        - OS: [e.g. Windows 11, macOS 14, Ubuntu 22.04]
        - Node.js version: [e.g. 18.17.0]
        - Project version: [e.g. 1.0.0]
        - Browser: [e.g. Chrome 120, Firefox 121]

  - type: textarea
    id: research
    attributes:
      label: Research Done
      description: What have you already tried to find the answer?
      placeholder: |
        - Documentation sections checked
        - Search terms used
        - Similar issues or discussions found
        - External resources consulted

  - type: checkboxes
    id: checklist
    attributes:
      label: Checklist
      description: Please confirm the following before submitting.
      options:
        - label: I have searched the documentation for an answer
          required: true
        - label: I have searched existing issues and discussions
          required: true
        - label: I have provided sufficient context for my question
          required: true
