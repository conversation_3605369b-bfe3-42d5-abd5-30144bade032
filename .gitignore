# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out/

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Logs
logs
*.log

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Temporary files
*.tmp
*.temp

# Build outputs
build/
dist/

# Test outputs
test-results/
coverage/

# Database
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup

# Archive files
*.zip
*.tar.gz
*.rar

# Local configuration files
config.local.js
config.local.json

# MCP specific
mcp-config.json
.mcp/

# AI/ML model files
*.model
*.weights
*.h5
*.pkl
*.joblib

# Large data files
*.csv
*.json.gz
*.parquet

# Secrets and keys
*.pem
*.key
*.crt
*.p12
secrets/
keys/

# Docker
.dockerignore
docker-compose.override.yml

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Python (if any Python scripts are added)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Java (if any Java components are added)
*.class
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar
target/

# C/C++ (if any native modules)
*.o
*.a
*.so
*.dylib
*.dll

# Rust (if any Rust components)
target/
Cargo.lock

# Go (if any Go components)
*.exe
*.exe~
*.dll
*.so
*.dylib
vendor/

# Ruby (if any Ruby scripts)
*.gem
*.rbc
/.config
/coverage/
/InstalledFiles
/pkg/
/spec/reports/
/spec/examples.txt
/test/tmp/
/test/version_tmp/
/tmp/

# PHP (if any PHP components)
/vendor/
composer.phar
composer.lock

# Monitoring and analytics
.sentryclirc

# Performance monitoring
.lighthouseci/

# Storybook
storybook-static/

# Chromatic
chromatic.log

# Vercel
.vercel

# Netlify
.netlify

# AWS
.aws/

# Google Cloud
.gcloud/

# Azure
.azure/

# Local development
.local/
local/

# Cache directories
.cache/
cache/

# Temporary directories
tmp/
temp/

# Lock files (keep package-lock.json but ignore others)
yarn.lock
pnpm-lock.yaml

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS
.DS_Store
Thumbs.db
