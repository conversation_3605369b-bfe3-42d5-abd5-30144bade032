import { anthropic } from "@ai-sdk/anthropic";
import { frontendTools } from "@assistant-ui/react-ai-sdk";
import { streamText, experimental_createMCPClient } from "ai";

export const runtime = "edge";
export const maxDuration = 30;

export async function POST(req: Request) {
  const { messages, system, tools } = await req.json();

  // Check if API key is configured
  if (!process.env.ANTHROPIC_API_KEY || process.env.ANTHROPIC_API_KEY === 'your_anthropic_api_key_here') {
    return new Response(
      JSON.stringify({
        error: 'Anthropic API key not configured. Please set ANTHROPIC_API_KEY in your .env.local file.'
      }),
      {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }

  let mcpClient;
  let mcpTools = {};

  try {
    // Connect to the n8n MCP server via SSE
    mcpClient = await experimental_createMCPClient({
      transport: {
        type: 'sse',
        url: 'https://n8n.datosoperations.com/mcp/b2a01660-20a1-4edd-8b31-f9cdaa1810e5/sse',
      },
    });

    // Get tools from the MCP server
    mcpTools = await mcpClient.tools();
    console.log('MCP Tools available:', Object.keys(mcpTools));
  } catch (error) {
    console.error('Failed to connect to MCP server:', error);
    // Continue without MCP tools if connection fails
  }

  const result = streamText({
    model: anthropic("claude-3-5-sonnet-20241022"),
    messages,
    // forward system prompt and tools from the frontend
    toolCallStreaming: true,
    system,
    tools: {
      ...frontendTools(tools),
      ...mcpTools, // Add MCP tools from n8n server
    },
    onError: console.log,
    onFinish: () => {
      // Clean up MCP client after streaming is complete
      if (mcpClient) {
        mcpClient.close().catch(console.error);
      }
    },
  });

  return result.toDataStreamResponse();
}
