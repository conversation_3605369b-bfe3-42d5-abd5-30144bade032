name: 🐛 Bug Report
description: Report a bug or unexpected behavior
title: "[Bug]: "
labels: ["bug", "needs-triage"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        Thank you for taking the time to report a bug! Please fill out the form below to help us understand and reproduce the issue.

  - type: textarea
    id: description
    attributes:
      label: Bug Description
      description: A clear and concise description of what the bug is.
      placeholder: Describe the bug...
    validations:
      required: true

  - type: textarea
    id: reproduction
    attributes:
      label: Steps to Reproduce
      description: Please provide detailed steps to reproduce the issue.
      placeholder: |
        1. Go to '...'
        2. Click on '...'
        3. Scroll down to '...'
        4. See error
    validations:
      required: true

  - type: textarea
    id: expected
    attributes:
      label: Expected Behavior
      description: What did you expect to happen?
      placeholder: Describe what you expected to happen...
    validations:
      required: true

  - type: textarea
    id: actual
    attributes:
      label: Actual Behavior
      description: What actually happened?
      placeholder: Describe what actually happened...
    validations:
      required: true

  - type: textarea
    id: environment
    attributes:
      label: Environment
      description: Please provide information about your environment.
      placeholder: |
        - OS: [e.g. Windows 11, macOS 14, Ubuntu 22.04]
        - Browser: [e.g. Chrome 120, Firefox 121, Safari 17]
        - Node.js version: [e.g. 18.17.0]
        - npm version: [e.g. 9.6.7]
        - Project version: [e.g. 1.0.0]
    validations:
      required: true

  - type: textarea
    id: logs
    attributes:
      label: Error Logs
      description: Please provide any relevant error logs or console output.
      placeholder: Paste error logs here...
      render: shell

  - type: textarea
    id: screenshots
    attributes:
      label: Screenshots
      description: If applicable, add screenshots to help explain your problem.
      placeholder: Drag and drop screenshots here or paste image URLs...

  - type: textarea
    id: additional
    attributes:
      label: Additional Context
      description: Add any other context about the problem here.
      placeholder: Any additional information that might be helpful...

  - type: checkboxes
    id: checklist
    attributes:
      label: Checklist
      description: Please confirm the following before submitting.
      options:
        - label: I have searched for existing issues that might be related to this bug
          required: true
        - label: I have provided all the required information above
          required: true
        - label: I have tested this with the latest version of the project
          required: true
        - label: I am willing to help test a fix for this issue
          required: false
