name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        cache-dependency-path: |
          package-lock.json
          streaming-ai-agent/package-lock.json
    
    - name: Install root dependencies
      run: npm ci
      
    - name: Install app dependencies
      run: |
        cd streaming-ai-agent
        npm ci
    
    - name: Run linting
      run: |
        cd streaming-ai-agent
        npm run lint
    
    - name: Run type checking
      run: |
        cd streaming-ai-agent
        npm run type-check
    
    - name: Run tests
      run: |
        cd streaming-ai-agent
        npm test
      env:
        CI: true
    
    - name: Build application
      run: |
        cd streaming-ai-agent
        npm run build
      env:
        ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}

  security:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Use Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        cache-dependency-path: |
          package-lock.json
          streaming-ai-agent/package-lock.json
    
    - name: Install dependencies
      run: |
        npm ci
        cd streaming-ai-agent
        npm ci
    
    - name: Run security audit
      run: |
        npm audit --audit-level=high
        cd streaming-ai-agent
        npm audit --audit-level=high

  dependency-review:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Dependency Review
      uses: actions/dependency-review-action@v4
