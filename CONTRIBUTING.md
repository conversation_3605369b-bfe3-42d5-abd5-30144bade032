# Contributing to Streaming N8N Chatbot

Thank you for your interest in contributing to the Streaming N8N Chatbot project! We welcome contributions from the community and are grateful for your support.

## 🤝 How to Contribute

### Reporting Issues

Before creating an issue, please:

1. **Search existing issues** to avoid duplicates
2. **Use the issue templates** when available
3. **Provide detailed information** including:
   - Steps to reproduce the issue
   - Expected vs actual behavior
   - Environment details (OS, Node.js version, browser)
   - Screenshots or error logs if applicable

### Suggesting Features

We love feature suggestions! Please:

1. **Check existing feature requests** first
2. **Use the feature request template**
3. **Explain the use case** and why it would be valuable
4. **Consider the scope** - smaller, focused features are easier to implement

### Code Contributions

#### Getting Started

1. **Fork the repository** on GitHub
2. **Clone your fork** locally:
   ```bash
   git clone https://github.com/yourusername/streaming-n8n-chatbot.git
   cd streaming-n8n-chatbot
   ```

3. **Create a feature branch**:
   ```bash
   git checkout -b feature/your-feature-name
   ```

4. **Install dependencies**:
   ```bash
   npm install
   cd streaming-ai-agent
   npm install
   ```

5. **Set up your development environment**:
   - Copy `.env.local.example` to `.env.local`
   - Add your Anthropic API key
   - Configure any other required environment variables

#### Development Guidelines

##### Code Style

- **TypeScript**: Use TypeScript for all new code
- **ESLint**: Follow the existing ESLint configuration
- **Prettier**: Code formatting is handled by Prettier
- **Naming**: Use descriptive names for variables, functions, and components

##### Component Guidelines

- **React Components**: Use functional components with hooks
- **File Structure**: Follow the existing directory structure
- **Imports**: Use absolute imports where configured
- **Props**: Define proper TypeScript interfaces for component props

##### API Guidelines

- **Route Handlers**: Follow Next.js App Router conventions
- **Error Handling**: Implement proper error handling and logging
- **Validation**: Validate input data using appropriate libraries
- **Documentation**: Document API endpoints and their parameters

#### Testing

- **Write Tests**: Include tests for new features and bug fixes
- **Test Types**: Unit tests for utilities, integration tests for components
- **Coverage**: Aim for good test coverage on critical paths
- **Run Tests**: Ensure all tests pass before submitting

```bash
cd streaming-ai-agent
npm test
npm run test:coverage
```

#### Commit Guidelines

We follow conventional commits for clear commit history:

- **feat**: New features
- **fix**: Bug fixes
- **docs**: Documentation changes
- **style**: Code style changes (formatting, etc.)
- **refactor**: Code refactoring
- **test**: Adding or updating tests
- **chore**: Maintenance tasks

Examples:
```
feat: add streaming response indicators
fix: resolve MCP connection timeout issue
docs: update installation instructions
```

#### Pull Request Process

1. **Update Documentation**: Update README.md or other docs if needed
2. **Add Tests**: Include appropriate tests for your changes
3. **Check CI**: Ensure all CI checks pass
4. **Request Review**: Request review from maintainers
5. **Address Feedback**: Respond to review comments promptly

##### Pull Request Template

When creating a PR, please include:

- **Description**: Clear description of what the PR does
- **Motivation**: Why this change is needed
- **Testing**: How you tested the changes
- **Screenshots**: For UI changes, include before/after screenshots
- **Breaking Changes**: Note any breaking changes
- **Related Issues**: Link to related issues

## 🏗️ Development Setup

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm
- Git
- Anthropic API key

### Local Development

1. **Start the development server**:
   ```bash
   cd streaming-ai-agent
   npm run dev
   ```

2. **Run linting**:
   ```bash
   npm run lint
   npm run lint:fix
   ```

3. **Type checking**:
   ```bash
   npm run type-check
   ```

4. **Build for production**:
   ```bash
   npm run build
   ```

### Environment Variables

Required for development:
```env
ANTHROPIC_API_KEY=your_key_here
```

Optional:
```env
MCP_SERVER_URL=your_mcp_server_url
NODE_ENV=development
```

## 📋 Project Structure

```
streaming-n8n-chatbot/
├── streaming-ai-agent/          # Main Next.js app
│   ├── app/                     # App Router pages and API
│   ├── components/              # React components
│   ├── lib/                     # Utility functions
│   ├── hooks/                   # Custom React hooks
│   └── types/                   # TypeScript type definitions
├── docs/                        # Additional documentation
├── .github/                     # GitHub templates and workflows
└── README.md                    # Main documentation
```

## 🔍 Code Review Process

### For Contributors

- **Self-review**: Review your own code before submitting
- **Small PRs**: Keep pull requests focused and reasonably sized
- **Clear Commits**: Write clear commit messages
- **Documentation**: Update relevant documentation

### For Reviewers

- **Be Constructive**: Provide helpful, constructive feedback
- **Be Timely**: Review PRs in a reasonable timeframe
- **Test Changes**: Test the changes locally when possible
- **Check Standards**: Ensure code follows project standards

## 🚀 Release Process

1. **Version Bump**: Update version numbers following semantic versioning
2. **Changelog**: Update CHANGELOG.md with new features and fixes
3. **Testing**: Ensure all tests pass and manual testing is complete
4. **Documentation**: Update documentation as needed
5. **Release**: Create a GitHub release with release notes

## 📞 Getting Help

- **GitHub Discussions**: For questions and general discussion
- **GitHub Issues**: For bug reports and feature requests
- **Documentation**: Check the wiki and README files

## 🙏 Recognition

Contributors will be recognized in:
- **README.md**: Contributors section
- **Release Notes**: Major contributions highlighted
- **GitHub**: Contributor graphs and statistics

Thank you for contributing to Streaming N8N Chatbot! 🎉
