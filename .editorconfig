# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# All files
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 2

# JavaScript, TypeScript, JSX, TSX
[*.{js,jsx,ts,tsx}]
indent_style = space
indent_size = 2
max_line_length = 100

# JSON files
[*.json]
indent_style = space
indent_size = 2

# YAML files
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# Markdown files
[*.md]
trim_trailing_whitespace = false
max_line_length = off

# HTML files
[*.html]
indent_style = space
indent_size = 2

# CSS, SCSS, SASS files
[*.{css,scss,sass}]
indent_style = space
indent_size = 2

# Configuration files
[*.{config.js,config.ts,config.mjs}]
indent_style = space
indent_size = 2

# Package files
[package.json]
indent_style = space
indent_size = 2

# Lock files (don't modify)
[{package-lock.json,yarn.lock,pnpm-lock.yaml}]
insert_final_newline = false
trim_trailing_whitespace = false

# Environment files
[.env*]
insert_final_newline = true
trim_trailing_whitespace = true

# Shell scripts
[*.{sh,bash,zsh}]
indent_style = space
indent_size = 2
end_of_line = lf

# Windows batch files
[*.{bat,cmd}]
end_of_line = crlf

# PowerShell files
[*.ps1]
end_of_line = crlf

# Makefile
[Makefile]
indent_style = tab

# Docker files
[{Dockerfile,*.dockerfile}]
indent_style = space
indent_size = 2

# Git files
[.git*]
insert_final_newline = true
trim_trailing_whitespace = true

# License and documentation
[{LICENSE,CHANGELOG,CONTRIBUTING,README}*]
insert_final_newline = true
trim_trailing_whitespace = true
max_line_length = off
