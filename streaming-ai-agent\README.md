# Streaming AI Agent with n8n MCP Integration

A modern AI chat interface built with <PERSON>.js, assistant-ui, and integrated with n8n's Model Context Protocol (MCP) server for enhanced tool capabilities.

## Features

- 🚀 **Streaming Chat Interface**: Built with assistant-ui for a ChatGPT-like experience
- 🤖 **Claude 3.5 Sonnet**: Powered by <PERSON>throp<PERSON>'s latest model
- 🔧 **n8n MCP Integration**: Access to n8n tools via Model Context Protocol
- ⚡ **Real-time Streaming**: Server-sent events for live responses
- 🎨 **Modern UI**: Beautiful interface with Tailwind CSS and Radix UI

## Quick Start

### Prerequisites

- Node.js 18+
- Anthropic API key
- Access to the n8n MCP server

### Installation

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Set up environment variables:**
   Edit `.env.local` and add your Anthropic API key:
   ```
   ANTHROPIC_API_KEY=your_anthropic_api_key_here
   ```

3. **Start the development server:**
   ```bash
   npm run dev
   ```

4. **Open your browser:**
   Navigate to [http://localhost:3000](http://localhost:3000)

## Architecture

### Frontend
- **Next.js 15**: React framework with App Router
- **assistant-ui**: React components for AI chat interface
- **Tailwind CSS**: Utility-first CSS framework
- **Radix UI**: Accessible component primitives

### Backend
- **Vercel AI SDK**: Streaming AI responses
- **MCP Client**: Integration with n8n tools
- **Claude 3.5 Sonnet**: AI model for conversations

### MCP Integration
The application connects to the n8n MCP server at:
```
https://n8n.datosoperations.com/mcp/b2a01660-20a1-4edd-8b31-f9cdaa1810e5/sse
```

This provides access to n8n's workflow automation tools directly within the chat interface.

## Usage

1. **Start a conversation**: Type your message in the chat interface
2. **Use n8n tools**: The AI can automatically access and use tools from the n8n MCP server
3. **Stream responses**: Watch as responses are generated in real-time
4. **Tool integration**: The AI can perform actions using n8n workflows

## Development

### Project Structure
```
streaming-ai-agent/
├── app/
│   ├── api/chat/route.ts    # API endpoint with MCP integration
│   ├── assistant.tsx        # Main chat interface
│   └── page.tsx            # Home page
├── components/             # UI components
├── .env.local             # Environment variables
└── package.json           # Dependencies
```

### Key Files
- `app/api/chat/route.ts`: Handles streaming chat with MCP integration
- `app/assistant.tsx`: Main chat interface using assistant-ui
- `.env.local`: Configuration for API keys

## Troubleshooting

### Common Issues

1. **MCP Connection Failed**: Check the n8n server URL and network connectivity
2. **API Key Issues**: Ensure your Anthropic API key is correctly set
3. **Streaming Problems**: Verify the API route is properly configured

### Logs
Check the browser console and server logs for detailed error information.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
