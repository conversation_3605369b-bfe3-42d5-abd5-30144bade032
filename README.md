# Streaming N8N Chatbot

A modern AI-powered chatbot with streaming capabilities, built with Next.js and integrated with n8n's Model Context Protocol (MCP) server for enhanced automation and workflow capabilities.

## 🚀 Features

- **Streaming AI Chat Interface**: Real-time conversation with Claude 3.5 Sonnet
- **n8n MCP Integration**: Direct access to n8n workflow automation tools
- **Modern UI/UX**: Built with Next.js, Tailwind CSS, and Radix UI components
- **Real-time Streaming**: Server-sent events for live AI responses
- **Tool Integration**: AI can execute n8n workflows and automation tasks
- **Responsive Design**: Works seamlessly across desktop and mobile devices

## 🏗️ Architecture

```
streaming-n8n-chatbot/
├── streaming-ai-agent/          # Main Next.js application
│   ├── app/                     # Next.js App Router
│   │   ├── api/chat/           # Chat API with MCP integration
│   │   ├── assistant.tsx       # Main chat interface
│   │   └── page.tsx           # Home page
│   ├── components/             # Reusable UI components
│   │   ├── ui/                # Base UI components (Radix UI)
│   │   └── assistant-ui/      # Chat-specific components
│   └── lib/                   # Utility functions
├── package.json               # Root dependencies
└── README.md                 # This file
```

## 🛠️ Tech Stack

### Frontend
- **Next.js 15**: React framework with App Router
- **TypeScript**: Type-safe development
- **Tailwind CSS**: Utility-first CSS framework
- **Radix UI**: Accessible component primitives
- **assistant-ui**: Specialized React components for AI chat interfaces

### Backend & AI
- **Vercel AI SDK**: Streaming AI responses and tool integration
- **Anthropic Claude 3.5 Sonnet**: Advanced language model
- **Model Context Protocol (MCP)**: Integration with n8n tools
- **Server-Sent Events**: Real-time streaming communication

### Integration
- **n8n MCP Server**: Workflow automation and tool access
- **RESTful APIs**: Communication with external services

## 🚀 Quick Start

### Prerequisites

- Node.js 18 or higher
- npm, yarn, or pnpm
- Anthropic API key
- Access to n8n MCP server

### Installation

1. **Clone the repository:**
   ```bash
   git clone https://github.com/yourusername/streaming-n8n-chatbot.git
   cd streaming-n8n-chatbot
   ```

2. **Install root dependencies:**
   ```bash
   npm install
   ```

3. **Navigate to the main application:**
   ```bash
   cd streaming-ai-agent
   npm install
   ```

4. **Set up environment variables:**
   Create a `.env.local` file in the `streaming-ai-agent` directory:
   ```env
   ANTHROPIC_API_KEY=your_anthropic_api_key_here
   ```

5. **Start the development server:**
   ```bash
   npm run dev
   ```

6. **Open your browser:**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🔧 Configuration

### Environment Variables

Create a `.env.local` file in the `streaming-ai-agent` directory with the following variables:

```env
# Required
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Optional - MCP Configuration
MCP_SERVER_URL=https://n8n.datosoperations.com/mcp/b2a01660-20a1-4edd-8b31-f9cdaa1810e5/sse
```

### n8n MCP Integration

The application connects to the n8n MCP server to provide AI access to workflow automation tools. The default configuration connects to:

```
https://n8n.datosoperations.com/mcp/b2a01660-20a1-4edd-8b31-f9cdaa1810e5/sse
```

## 📖 Usage

1. **Start a Conversation**: Type your message in the chat interface
2. **AI Tool Usage**: The AI can automatically access and execute n8n workflows
3. **Real-time Responses**: Watch responses stream in real-time
4. **Workflow Integration**: Ask the AI to perform automation tasks using n8n

### Example Interactions

- "Create a new workflow in n8n"
- "Execute the data processing workflow"
- "Show me the status of my automation tasks"
- "Help me set up a webhook integration"

## 🧪 Development

### Running Tests

```bash
cd streaming-ai-agent
npm test
```

### Building for Production

```bash
cd streaming-ai-agent
npm run build
```

### Linting and Code Quality

```bash
cd streaming-ai-agent
npm run lint
npm run type-check
```

## 🚀 Deployment

### Vercel (Recommended)

1. Connect your GitHub repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Docker

```bash
cd streaming-ai-agent
docker build -t streaming-n8n-chatbot .
docker run -p 3000:3000 streaming-n8n-chatbot
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guidelines](CONTRIBUTING.md) for details.

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the [Wiki](../../wiki) for detailed guides
- **Issues**: Report bugs or request features via [GitHub Issues](../../issues)
- **Discussions**: Join the conversation in [GitHub Discussions](../../discussions)

## 🙏 Acknowledgments

- [Anthropic](https://anthropic.com) for Claude AI
- [n8n](https://n8n.io) for workflow automation
- [Vercel](https://vercel.com) for the AI SDK
- [Next.js](https://nextjs.org) team for the amazing framework

---

**Built with ❤️ for the AI automation community**
