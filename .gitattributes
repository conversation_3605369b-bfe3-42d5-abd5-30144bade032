# Auto detect text files and perform LF normalization
* text=auto

# Source code
*.js text eol=lf
*.jsx text eol=lf
*.ts text eol=lf
*.tsx text eol=lf
*.json text eol=lf
*.md text eol=lf
*.yml text eol=lf
*.yaml text eol=lf
*.xml text eol=lf
*.html text eol=lf
*.css text eol=lf
*.scss text eol=lf
*.sass text eol=lf
*.less text eol=lf

# Configuration files
*.config.js text eol=lf
*.config.ts text eol=lf
*.config.mjs text eol=lf
.env* text eol=lf
.gitignore text eol=lf
.gitattributes text eol=lf
.eslintrc* text eol=lf
.prettierrc* text eol=lf
tsconfig.json text eol=lf
package.json text eol=lf
package-lock.json text eol=lf

# Documentation
*.txt text eol=lf
*.md text eol=lf
*.rst text eol=lf
LICENSE text eol=lf
CHANGELOG* text eol=lf
CONTRIBUTING* text eol=lf
README* text eol=lf

# Shell scripts
*.sh text eol=lf
*.bash text eol=lf
*.zsh text eol=lf

# Windows scripts
*.bat text eol=crlf
*.cmd text eol=crlf
*.ps1 text eol=crlf

# Binary files
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.svg binary
*.webp binary
*.avif binary

# Fonts
*.woff binary
*.woff2 binary
*.eot binary
*.ttf binary
*.otf binary

# Archives
*.zip binary
*.tar binary
*.gz binary
*.rar binary
*.7z binary

# Audio/Video
*.mp3 binary
*.mp4 binary
*.avi binary
*.mov binary
*.wav binary
*.flac binary

# Documents
*.pdf binary
*.doc binary
*.docx binary
*.xls binary
*.xlsx binary
*.ppt binary
*.pptx binary

# Database
*.db binary
*.sqlite binary
*.sqlite3 binary

# Compiled files
*.exe binary
*.dll binary
*.so binary
*.dylib binary
*.class binary
*.jar binary

# Node.js specific
node_modules/ export-ignore
npm-debug.log* export-ignore
yarn-debug.log* export-ignore
yarn-error.log* export-ignore

# Build outputs
.next/ export-ignore
out/ export-ignore
dist/ export-ignore
build/ export-ignore

# Cache directories
.cache/ export-ignore
.parcel-cache/ export-ignore

# Environment files
.env export-ignore
.env.local export-ignore
.env.development.local export-ignore
.env.test.local export-ignore
.env.production.local export-ignore

# IDE files
.vscode/ export-ignore
.idea/ export-ignore

# OS files
.DS_Store export-ignore
Thumbs.db export-ignore

# Logs
*.log export-ignore
logs/ export-ignore

# Coverage
coverage/ export-ignore
.nyc_output export-ignore

# Language-specific settings
*.js linguist-language=JavaScript
*.jsx linguist-language=JavaScript
*.ts linguist-language=TypeScript
*.tsx linguist-language=TypeScript
*.md linguist-documentation
*.json linguist-generated=false

# Diff settings for better merge conflict resolution
*.json merge=ours
package-lock.json merge=ours
yarn.lock merge=ours
pnpm-lock.yaml merge=ours

# Security - mark sensitive files
*.key filter=git-crypt diff=git-crypt
*.pem filter=git-crypt diff=git-crypt
*.p12 filter=git-crypt diff=git-crypt
secrets/* filter=git-crypt diff=git-crypt

# Large file handling (if using Git LFS)
# *.zip filter=lfs diff=lfs merge=lfs -text
# *.tar.gz filter=lfs diff=lfs merge=lfs -text
# *.mp4 filter=lfs diff=lfs merge=lfs -text
# *.mov filter=lfs diff=lfs merge=lfs -text

# Export ignore for GitHub releases
.github/ export-ignore
.gitignore export-ignore
.gitattributes export-ignore
tests/ export-ignore
test/ export-ignore
spec/ export-ignore
docs/ export-ignore
*.test.* export-ignore
*.spec.* export-ignore
